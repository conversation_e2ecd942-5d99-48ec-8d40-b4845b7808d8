{"name": "bime-h5", "version": "0.1.0", "private": true, "config": {"buildVersion": "1.1.0.32dbe51"}, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@element-plus/icons-vue": "^2.0.4", "animate.css": "^4.1.1", "axios": "^0.27.2", "core-js": "^3.6.5", "crypto-js": "^4.2.0", "echarts": "^5.3.2", "element-plus": "^2.2.4", "qs": "^6.10.5", "vant": "^4.7.2", "vue": "^3.0.0", "vue-router": "^4.0.0-0", "vuex": "^4.0.0-0", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.13", "@vue/cli-plugin-eslint": "~4.5.13", "@vue/cli-plugin-router": "~4.5.13", "@vue/cli-plugin-vuex": "~4.5.13", "@vue/cli-service": "~4.5.13", "@vue/compiler-sfc": "^3.0.0", "autoprefixer": "^8.0.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^7.0.0", "postcss": "8.4.14", "postcss-loader": "^6.1.1", "postcss-px-to-viewport": "1.1.1", "postcss-scss": "4.0.4", "sass": "^1.26.5", "sass-loader": "^8.0.2", "svg-sprite-loader": "^6.0.11", "unplugin-vue-components": "^0.25.2"}}